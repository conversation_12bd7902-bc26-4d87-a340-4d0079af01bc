import React from 'react';
import { Modal, Tag, Typography, Space, Badge, Avatar, Toolt<PERSON>, Button } from 'antd';
import {
  CalendarOutlined,
  UserOutlined,
  TagOutlined,
  LinkOutlined,
  PushpinOutlined,
  ClockCircleOutlined,
  MessageOutlined,
  WarningOutlined,
  ExclamationCircleOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import PropTypes from 'prop-types';
import styles from './view-brief-modal.module.scss';

const { Title, Text, Paragraph } = Typography;

const ViewBriefModal = ({ brief, open, onClose }) => {
  const getPriorityIcon = (priority) => {
    switch (priority.toLowerCase()) {
      case 'critical':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'high':
        return <WarningOutlined style={{ color: '#faad14' }} />;
      case 'medium':
        return <QuestionCircleOutlined style={{ color: '#1890ff' }} />;
      case 'low':
        return <QuestionCircleOutlined style={{ color: '#52c41a' }} />;
      default:
        return null;
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority.toLowerCase()) {
      case 'critical':
        return '#ff4d4f';
      case 'high':
        return '#faad14';
      case 'medium':
        return '#1890ff';
      case 'low':
        return '#52c41a';
      default:
        return '#d9d9d9';
    }
  };

  if (!brief) return null;

  return (
    <Modal
      open={open}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="close" onClick={onClose}>
          Cerrar
        </Button>,
      ]}>
      <div className={styles.briefHeader}>
        {brief.isPinned && (
          <Tag icon={<PushpinOutlined />} color="red">
            Fijado
          </Tag>
        )}
        <Badge
          color={getPriorityColor(brief.priority)}
          text={
            <Space>
              {getPriorityIcon(brief.priority)}
              <Text>{brief.priority.charAt(0).toUpperCase() + brief.priority.slice(1)}</Text>
            </Space>
          }
        />
        <Title level={4}>{brief.title}</Title>
      </div>

      <div className={styles.briefMeta}>
        <Space wrap>
          <Tag icon={<UserOutlined />} color="blue">
            {brief.author}
          </Tag>
          <Tooltip title="Fecha de creación">
            <Tag icon={<CalendarOutlined />} color="default">
              {new Date(brief.createdAt).toLocaleDateString()}
            </Tag>
          </Tooltip>
          {brief.expiresAt && (
            <Tooltip title="Fecha de expiración">
              <Tag icon={<ClockCircleOutlined />} color="orange">
                Expira: {new Date(brief.expiresAt).toLocaleDateString()}
              </Tag>
            </Tooltip>
          )}
          <Tag icon={<MessageOutlined />} color="cyan">
            {brief.commentsCount} comentarios
          </Tag>
        </Space>
      </div>

      <div className={styles.briefContent}>
        <Paragraph>{brief.description}</Paragraph>
      </div>

      {brief.tags && brief.tags.length > 0 && (
        <div className={styles.briefTags}>
          <Text type="secondary">
            <TagOutlined /> Tags:
          </Text>
          <Space wrap style={{ marginLeft: 8 }}>
            {brief.tags.map((tag) => (
              <Tag key={tag} color="blue">
                #{tag}
              </Tag>
            ))}
          </Space>
        </div>
      )}

      {/* Aquí puedes agregar más secciones según necesites, como archivos adjuntos o enlaces */}
    </Modal>
  );
};

ViewBriefModal.propTypes = {
  brief: PropTypes.shape({
    id: PropTypes.number,
    title: PropTypes.string,
    description: PropTypes.string,
    type: PropTypes.string,
    priority: PropTypes.string,
    tags: PropTypes.arrayOf(PropTypes.string),
    author: PropTypes.string,
    createdAt: PropTypes.instanceOf(Date),
    expiresAt: PropTypes.instanceOf(Date),
    commentsCount: PropTypes.number,
    isPinned: PropTypes.bool,
  }),
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default ViewBriefModal;

.modalHeader {
  margin-bottom: 32px;

  .modalTitle {
    margin: 16px 0 8px 0 !important;
    font-size: 24px;
    font-weight: 600;
  }

  .modalMeta {
    margin-bottom: 0;
  }
}

.contentSection {
  margin-bottom: 32px;
  background-color: #fafafffa;

  .sectionHeader {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 16px;
  }

  .contentBox {
    padding: 16px;
    background-color: #ffffff;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
  }
  .contentText {
    font-size: 1rem;
    margin-bottom: 0;
  }
}

.tagsSection {
  margin-bottom: 32px;

  .sectionHeader {
    display: flex;
    align-items: center;
    margin-bottom: 0.5px;
    font-size: 16px;
  }

  .tagsEditor {
    margin-top: 16px;
    padding: 16px;
    background-color: #fafafa;
    border-radius: 6px;
    border: 1px solid #f0f0f0;

    .selectedTags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 16px;

      .tag {
        display: inline-flex;
        align-items: center;
        padding: 4px 8px;
        background-color: #1890ff;
        color: white;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;

        .tagCloseButton {
          background: none;
          border: none;
          color: white;
          margin-left: 6px;
          cursor: pointer;
          font-size: 14px;
          line-height: 1;
          padding: 0;
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 2px;

          &:hover {
            background-color: rgba(255, 255, 255, 0.2);
          }
        }
      }
    }

    .tagInput {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 14px;
      margin-bottom: 12px;

      &:focus {
        outline: none;
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }

    .availableTags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .availableTag {
        padding: 4px 8px;
        background-color: #f0f0f0;
        color: #666;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        border: 1px solid transparent;
        transition: all 0.2s;

        &:hover {
          background-color: #e6f7ff;
          border-color: #1890ff;
          color: #1890ff;
        }
      }
    }

    .editorActions {
      display: flex;
      gap: 8px;
      margin-top: 16px;
      justify-content: flex-end;
    }
  }
}

.detailsSection {
  margin-bottom: 32px;

  .sectionHeader {
    display: flex;
    align-items: center;
    margin-bottom: 0.5px;
    font-size: 16px;
  }

  .detailsGrid {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .detailItem {
    display: flex;
    align-items: center;
  }
}

.commentsSection {
  .sectionHeader {
    display: flex;
    align-items: center;
    font-size: 16px;
  }

  .comment {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    .commentMeta {
      margin-top: 8px;
    }
  }

  .addComment {
    margin-top: 16px;
  }
}

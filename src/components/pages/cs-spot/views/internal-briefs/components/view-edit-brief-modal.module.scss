.modalHeader {
  margin-bottom: 32px;

  .modalTitle {
    margin: 16px 0 8px 0 !important;
    font-size: 24px;
    font-weight: 600;
  }

  .modalMeta {
    margin-bottom: 0;
  }
}

.contentSection {
  margin-bottom: 32px;
  background-color: #fafafffa;

  .sectionHeader {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 16px;
  }

  .contentBox {
    padding: 16px;
    background-color: #ffffff;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
  }
  .contentText {
    font-size: 1rem;
    margin-bottom: 0;
  }
}

.tagsSection {
  margin-bottom: 32px;

  .sectionHeader {
    display: flex;
    align-items: center;
    margin-bottom: 0.5px;
    font-size: 16px;
  }

  .tagsEditor {
    margin-top: 16px;

    .selectedTags {
      margin-bottom: 16px;
      display: flex;
      gap: 8px;
      flex-wrap: wrap;

      .tag {
        background-color: #5ba3f6;
        color: white;
        font-size: 14px;
        padding: 4px 12px;
        border-radius: 20px;
        display: inline-flex;
        align-items: center;
        gap: 8px;

        .tagCloseButton {
          background: none;
          border: none;
          color: white;
          cursor: pointer;
          font-size: 16px;
          line-height: 1;
          padding: 0;

          &:hover {
            opacity: 0.8;
          }
        }
      }
    }

    .tagInput {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 14px;
      margin-bottom: 16px;
      background-color: #f9fafb;
      color: #6b7280;

      &:focus {
        outline: none;
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }
    }

    .availableTags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .availableTag {
        padding: 8px 12px;
        background-color: transparent;
        color: #6b7280;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        border: none;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        gap: 4px;

        &:hover {
          background-color: #f3f4f6;
          color: #374151;
        }
      }
    }
  }
}

.detailsSection {
  margin-bottom: 32px;

  .sectionHeader {
    display: flex;
    align-items: center;
    margin-bottom: 0.5px;
    font-size: 16px;
  }

  .detailsGrid {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .detailItem {
    display: flex;
    align-items: center;
  }
}

.commentsSection {
  .sectionHeader {
    display: flex;
    align-items: center;
    font-size: 16px;
  }

  .comment {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    .commentMeta {
      margin-top: 8px;
    }
  }

  .addComment {
    margin-top: 16px;
  }
}

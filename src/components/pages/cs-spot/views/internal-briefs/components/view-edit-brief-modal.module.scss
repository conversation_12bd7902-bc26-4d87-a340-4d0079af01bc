.modalHeader {
  margin-bottom: 32px;

  .modalTitle {
    margin: 16px 0 8px 0 !important;
    font-size: 24px;
    font-weight: 600;
  }

  .modalMeta {
    margin-bottom: 0;
  }
}

.contentSection {
  margin-bottom: 32px;
  background-color: #fafafffa;

  .sectionHeader {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 16px;
  }

  .contentBox {
    padding: 16px;
    background-color: #ffffff;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
  }
  .contentText {
    font-size: 1rem;
    margin-bottom: 0;
  }
}

.tagsSection {
  margin-bottom: 32px;

  .sectionHeader {
    display: flex;
    align-items: center;
    margin-bottom: 0.5px;
    font-size: 16px;
  }
}

.detailsSection {
  margin-bottom: 32px;

  .sectionHeader {
    display: flex;
    align-items: center;
    margin-bottom: 0.5px;
    font-size: 16px;
  }

  .detailsGrid {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .detailItem {
    display: flex;
    align-items: center;
  }
}

.commentsSection {
  .sectionHeader {
    display: flex;
    align-items: center;
    font-size: 16px;
  }

  .comment {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    .commentMeta {
      margin-top: 8px;
    }
  }

  .addComment {
    margin-top: 16px;
  }
}

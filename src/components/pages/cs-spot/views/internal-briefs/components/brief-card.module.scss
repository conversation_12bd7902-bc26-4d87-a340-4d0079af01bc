.briefCard {
  background: #fff;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    box-shadow: 0 6px 16px rgba(255, 107, 107, 0.2);
    transform: translateY(-4px);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
  }

  &.pinnedCard {
    border-color: #ff6b6b;
    border-width: 2px;

    &:hover {
      border-color: #ff4d4f;
      box-shadow: 0 8px 20px rgba(255, 107, 107, 0.25);
      transform: translateY(-6px);
    }
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 8px;
}

.typeIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #e6f7ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  color: #2b6ef7;
  font-size: 14px;

  &.typeEvent {
    background: #e6f7ff;
    color: #1890ff;
    border-color: #91d5ff;
  }

  &.typeParentTrend {
    background: #fff7e6;
    color: #fa8c16;
    border-color: #ffd591;
  }

  &.typeMessaging {
    background: #f6ffed;
    color: #52c41a;
    border-color: #b7eb8f;
  }

  &.typeResource {
    background: #f0f5ff;
    color: #722ed1;
    border-color: #d3adf7;
  }

  &.typeOther {
    background: #f9f0ff;
    color: #722ed1;
    border-color: #d3adf7;
  }
}

.typeBadge {
  background: #e6f7ff;
  color: #2b6ef7;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #bfdbfe;

  &.typeEvent {
    background: #e6f7ff;
    color: #1890ff;
    border-color: #91d5ff;
  }

  &.typeParentTrend {
    background: #fff7e6;
    color: #fa8c16;
    border-color: #ffd591;
  }

  &.typeMessaging {
    background: #f6ffed;
    color: #52c41a;
    border-color: #b7eb8f;
  }

  &.typeResource {
    background: #f0f5ff;
    color: #722ed1;
    border-color: #d3adf7;
  }

  &.typeOther {
    background: #f9f0ff;
    color: #722ed1;
    border-color: #d3adf7;
  }
}

.priorityBadge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
}

.priorityCritical {
  background: #fff1f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.priorityHigh {
  background: #fff2e6;
  color: #ff8c00;
  border: 1px solid #ffd4a3;
}

.priorityMedium {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffe7ba;
}

.priorityLow {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.priorityDefault {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #d9d9d9;
}

.pinIcon {
  color: #ff4d4f;
  font-size: 16px;
  transform: rotate(45deg);
}

.title {
  font-size: 20px;
  font-weight: 600;
  color: #222;
  margin: 0 0 12px 0;
  line-height: 1.3;
}

.description {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin: 0 0 16px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.tagsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.tag {
  background: #f5f5f5;
  color: #666;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.moreTag {
  background: transparent;
  color: #8a94a6;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.authorInfo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.separator {
  color: #d9d9d9;
  font-size: 12px;
}

.timeAgo {
  font-size: 14px;
  color: #8a94a6;
}

.commentsInfo {
  display: flex;
  align-items: center;
  gap: 4px;
}

.commentIcon {
  color: #2b6ef7;
  font-size: 14px;
}

.commentsCount {
  color: #2b6ef7;
  font-size: 14px;
  font-weight: 500;
}

// Responsive design
@media (max-width: 768px) {
  .briefCard {
    padding: 16px;
  }

  .title {
    font-size: 18px;
  }

  .description {
    font-size: 13px;
  }

  .headerLeft {
    gap: 6px;
  }

  .typeIcon {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .typeBadge,
  .priorityBadge {
    font-size: 12px;
    padding: 3px 8px;
  }
}

@media (max-width: 480px) {
  .briefCard {
    padding: 12px;
  }

  .header {
    margin-bottom: 12px;
  }

  .title {
    font-size: 16px;
    margin-bottom: 8px;
  }

  .description {
    margin-bottom: 12px;
  }

  .tagsContainer {
    margin-bottom: 12px;
  }

  .footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

import React, { useState } from 'react';
import { Modal, Tag, Typography, Space, Badge, Avatar, Tooltip, Button, Input, Divider } from 'antd';
import {
  CalendarOutlined,
  UserOutlined,
  TagOutlined,
  PushpinOutlined,
  ClockCircleOutlined,
  MessageOutlined,
  WarningOutlined,
  ExclamationCircleOutlined,
  QuestionCircleOutlined,
  EditOutlined,
  TrendingUpOutlined,
  FileTextOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import PropTypes from 'prop-types';
import styles from './view-edit-brief-modal.module.scss';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const ViewEditBriefModal = ({ brief, open, onClose }) => {
  const [newComment, setNewComment] = useState('');

  const getPriorityIcon = (priority) => {
    switch (priority.toLowerCase()) {
      case 'critical':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'high':
        return <WarningOutlined style={{ color: '#faad14' }} />;
      case 'medium':
        return <QuestionCircleOutlined style={{ color: '#1890ff' }} />;
      case 'low':
        return <QuestionCircleOutlined style={{ color: '#52c41a' }} />;
      default:
        return null;
    }
  };

  const getTypeIcon = (type) => {
    switch (type.toLowerCase()) {
      case 'parent trend':
        return <TrendingUpOutlined />;
      case 'event':
        return <CalendarOutlined />;
      case 'messaging':
        return <MessageOutlined />;
      case 'resource':
        return <FileTextOutlined />;
      default:
        return <FileTextOutlined />;
    }
  };

  const getTypeColor = (type) => {
    switch (type.toLowerCase()) {
      case 'parent trend':
        return 'orange';
      case 'event':
        return 'blue';
      case 'messaging':
        return 'green';
      case 'resource':
        return 'purple';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority.toLowerCase()) {
      case 'critical':
        return '#ff4d4f';
      case 'high':
        return '#faad14';
      case 'medium':
        return '#1890ff';
      case 'low':
        return '#52c41a';
      default:
        return '#d9d9d9';
    }
  };

  const handleAddComment = () => {
    if (newComment.trim()) {
      // Aquí agregarías el comentario a la data
      console.log('Adding comment:', newComment);
      setNewComment('');
    }
  };

  if (!brief) return null;

  return (
    <Modal open={open} onCancel={onClose} width={800} footer={null} closeIcon={<CloseOutlined />}>
      {/* Header con tags y título */}
      <div className={styles.modalHeader}>
        <Space wrap>
          <Tag icon={getTypeIcon(brief.type)} color={getTypeColor(brief.type)}>
            {brief.type}
          </Tag>
          {brief.priority === 'critical' && (
            <Tag icon={<WarningOutlined />} color="red">
              Urgent
            </Tag>
          )}
          {brief.isPinned && (
            <Tag icon={<PushpinOutlined />} color="red">
              Pinned
            </Tag>
          )}
        </Space>

        <Title level={2} className={styles.modalTitle}>
          {brief.title}
        </Title>

        <div className={styles.modalMeta}>
          <Text type="secondary">
            By {brief.author} (SPOT) • {new Date(brief.createdAt).toLocaleDateString()} •
            {brief.expiresAt && ` Expires ${new Date(brief.expiresAt).toLocaleDateString()}`}
          </Text>
        </div>
      </div>

      {/* Sección Content */}
      <div className={styles.contentSection}>
        <div className={styles.sectionHeader}>
          <FileTextOutlined style={{ marginRight: 8 }} />
          <Text strong>Content</Text>
        </div>
        <div className={styles.contentBox}>
          <Paragraph>{brief.description}</Paragraph>
        </div>
      </div>

      {/* Sección Tags */}
      {brief.tags && brief.tags.length > 0 && (
        <div className={styles.tagsSection}>
          <div className={styles.sectionHeader}>
            <TagOutlined style={{ marginRight: 8 }} />
            <Text strong>Tags</Text>
            <Button type="text" size="small" icon={<EditOutlined />} style={{ marginLeft: 'auto', color: '#1890ff' }}>
              Edit
            </Button>
          </div>
          <Space wrap>
            {brief.tags.map((tag) => (
              <Tag key={tag} color="blue">
                #{tag}
              </Tag>
            ))}
          </Space>
        </div>
      )}

      {/* Sección Details */}
      <div className={styles.detailsSection}>
        <div className={styles.sectionHeader}>
          <ClockCircleOutlined style={{ marginRight: 8 }} />
          <Text strong>Details</Text>
        </div>
        <div className={styles.detailsGrid}>
          <div className={styles.detailItem}>
            <CalendarOutlined style={{ marginRight: 8 }} />
            <Text strong>Created:</Text>
            <Text style={{ marginLeft: 8 }}>{new Date(brief.createdAt).toLocaleDateString()}</Text>
          </div>
          {brief.expiresAt && (
            <div className={styles.detailItem}>
              <ClockCircleOutlined style={{ marginRight: 8 }} />
              <Text strong>Expires:</Text>
              <Text style={{ marginLeft: 8 }}>{new Date(brief.expiresAt).toLocaleDateString()}</Text>
            </div>
          )}
          <div className={styles.detailItem}>
            <MessageOutlined style={{ marginRight: 8 }} />
            <Text strong>Comments:</Text>
            <Text style={{ marginLeft: 8 }}>{brief.commentsCount}</Text>
          </div>
        </div>
      </div>

      {/* Sección Comments */}
      <div className={styles.commentsSection}>
        <div className={styles.sectionHeader}>
          <MessageOutlined style={{ marginRight: 8 }} />
          <Text strong>Comments ({brief.commentsCount})</Text>
        </div>

        {/* Comentario de ejemplo */}
        {brief.commentsCount > 0 && (
          <div className={styles.comment}>
            <Text strong>I'll update our FAQ section and create talking points for this week's calls.</Text>
            <div className={styles.commentMeta}>
              <Text type="secondary">Alex Thompson (CP) • over 1 year ago</Text>
            </div>
          </div>
        )}

        {/* Input para nuevo comentario */}
        <div className={styles.addComment}>
          <TextArea
            placeholder="Add a comment..."
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            rows={3}
            style={{ marginBottom: 12 }}
          />
          <Button type="primary" onClick={handleAddComment} disabled={!newComment.trim()}>
            Add
          </Button>
        </div>
      </div>
    </Modal>
  );
};

ViewEditBriefModal.propTypes = {
  brief: PropTypes.shape({
    id: PropTypes.number,
    title: PropTypes.string,
    description: PropTypes.string,
    type: PropTypes.string,
    priority: PropTypes.string,
    tags: PropTypes.arrayOf(PropTypes.string),
    author: PropTypes.string,
    createdAt: PropTypes.instanceOf(Date),
    expiresAt: PropTypes.instanceOf(Date),
    commentsCount: PropTypes.number,
    isPinned: PropTypes.bool,
  }),
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default ViewEditBriefModal;

import React, { useState, useEffect } from 'react';
import { Modal, Tag, Typography, Space, Button, Input } from 'antd';
import {
  CalendarOutlined,
  TagOutlined,
  PushpinOutlined,
  ClockCircleOutlined,
  MessageOutlined,
  WarningOutlined,
  ExclamationCircleOutlined,
  QuestionCircleOutlined,
  EditOutlined,
  RiseOutlined,
  FileTextOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import PropTypes from 'prop-types';
import styles from './view-edit-brief-modal.module.scss';
import { Card } from 'components/card/card';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const ViewEditBriefModal = ({ brief, open, onClose }) => {
  const [newComment, setNewComment] = useState('');
  const [isEditingTags, setIsEditingTags] = useState(false);
  const [editedTags, setEditedTags] = useState([]);

  /*   const getPriorityIcon = (priority) => {
    switch (priority.toLowerCase()) {
      case 'critical':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'high':
        return <WarningOutlined style={{ color: '#faad14' }} />;
      case 'medium':
        return <QuestionCircleOutlined style={{ color: '#1890ff' }} />;
      case 'low':
        return <QuestionCircleOutlined style={{ color: '#52c41a' }} />;
      default:
        return null;
    }
  }; */

  const getTypeIcon = (type) => {
    switch (type.toLowerCase()) {
      case 'parent trend':
        return <RiseOutlined />;
      case 'event':
        return <CalendarOutlined />;
      case 'messaging':
        return <MessageOutlined />;
      case 'resource':
        return <FileTextOutlined />;
      default:
        return <FileTextOutlined />;
    }
  };

  const getTypeColor = (type) => {
    switch (type.toLowerCase()) {
      case 'parent trend':
        return 'orange';
      case 'event':
        return 'blue';
      case 'messaging':
        return 'green';
      case 'resource':
        return 'purple';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority.toLowerCase()) {
      case 'critical':
        return '#ff4d4f';
      case 'high':
        return '#faad14';
      case 'medium':
        return '#1890ff';
      case 'low':
        return '#52c41a';
      default:
        return '#d9d9d9';
    }
  };

  // Opciones de tags disponibles
  const tagOptions = [
    { id: 'urgent', name: '#urgent' },
    { id: 'deadline', name: '#deadline' },
    { id: 'open-house', name: '#open-house' },
    { id: 'admissions', name: '#admissions' },
    { id: 'financial-aid', name: '#financial-aid' },
    { id: 'event', name: '#event' },
    { id: 'reminder', name: '#reminder' },
    { id: 'announcement', name: '#announcement' },
    { id: 'online-learning', name: '#online-learning' },
    { id: 'parent-concerns', name: '#parent-concerns' },
    { id: 'trending', name: '#trending' },
    { id: 'messaging', name: '#messaging' },
    { id: 'leadership-approved', name: '#leadership-approved' },
    { id: 'call-scripts', name: '#call-scripts' },
    { id: 'email-templates', name: '#email-templates' },
    { id: 'Q2', name: '#Q2' },
    { id: 'enrollment-deadline', name: '#enrollment-deadline' },
    { id: 'april-campaign', name: '#april-campaign' },
    { id: 'testimonials', name: '#testimonials' },
    { id: 'updates', name: '#updates' },
    { id: 'operations', name: '#operations' },
    { id: 'summer-camp', name: '#summer-camp' },
    { id: 'registration', name: '#registration' },
    { id: 'STEM', name: '#STEM' },
    { id: 'grades-6-8', name: '#grades-6-8' },
    { id: 'system-maintenance', name: '#system-maintenance' },
    { id: 'downtime', name: '#downtime' },
    { id: 'fall-open-house', name: '#fall-open-house' },
    { id: 'planning', name: '#planning' },
    { id: 'innovation', name: '#innovation' },
    { id: 'spring-2024', name: '#spring-2024' },
  ];

  const handleEditTags = () => {
    setIsEditingTags(true);
    setEditedTags([...brief.tags]);
  };

  const handleSaveTags = () => {
    // Aquí implementarías la lógica para guardar los tags editados
    console.log('Saving tags:', editedTags);
    setIsEditingTags(false);
    // En una implementación real, actualizarías el brief con los nuevos tags
  };

  const handleCancelEditTags = () => {
    setIsEditingTags(false);
    setEditedTags([]);
  };

  const handleAddTag = (tagId) => {
    if (!editedTags.includes(tagId)) {
      setEditedTags([...editedTags, tagId]);
    }
  };

  const handleRemoveTag = (tagId) => {
    setEditedTags(editedTags.filter((id) => id !== tagId));
  };

  // Reset editing state when modal closes or brief changes
  useEffect(() => {
    if (!open) {
      setIsEditingTags(false);
      setEditedTags([]);
    }
  }, [open]);

  const handleAddComment = () => {
    if (newComment.trim()) {
      // Aquí agregarías el comentario a la data
      console.log('Adding comment:', newComment);
      setNewComment('');
    }
  };

  if (!brief) return null;

  return (
    <Modal open={open} onCancel={onClose} width={800} footer={null} closeIcon={<CloseOutlined />}>
      {/* Header con tags y título */}
      <div className={styles.modalHeader}>
        <Space wrap>
          <Tag icon={getTypeIcon(brief.type)} color={getTypeColor(brief.type)}>
            {brief.type}
          </Tag>
          {brief.priority === 'critical' && (
            <Tag icon={<WarningOutlined />} color="red">
              Urgent
            </Tag>
          )}
          {brief.isPinned && (
            <Tag icon={<PushpinOutlined />} color="red">
              Pinned
            </Tag>
          )}
        </Space>

        <Title level={2} className={styles.modalTitle}>
          {brief.title}
        </Title>

        <div className={styles.modalMeta}>
          <Text type="secondary">
            By {brief.author} (SPOT) • {new Date(brief.createdAt).toLocaleDateString()} •
            {brief.expiresAt && ` Expires ${new Date(brief.expiresAt).toLocaleDateString()}`}
          </Text>
        </div>
      </div>

      {/* Sección Content */}
      <Card className={`${styles.contentSection} spacing-p-16`}>
        <div className={styles.sectionHeader}>
          <FileTextOutlined style={{ marginRight: 8 }} />
          <Text strong>Content</Text>
        </div>
        <div className={styles.contentBox}>
          <Paragraph className={styles.contentText}>{brief.description}</Paragraph>
        </div>
      </Card>

      {/* Sección Tags */}
      {brief.tags && brief.tags.length > 0 && (
        <Card className={`${styles.tagsSection} spacing-p-16`}>
          <div className={styles.sectionHeader}>
            <TagOutlined style={{ marginRight: 8 }} />
            <Text strong>Tags</Text>
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              style={{ marginLeft: 'auto', color: '#1890ff' }}
              onClick={handleEditTags}>
              Edit
            </Button>
          </div>

          {!isEditingTags ? (
            <Space wrap>
              {brief.tags.map((tag) => (
                <Tag key={tag} color="blue">
                  #{tag}
                </Tag>
              ))}
            </Space>
          ) : (
            <div className={styles.tagsEditor}>
              <div className={styles.selectedTags}>
                {editedTags.map((tagId) => {
                  const tagObj = tagOptions.find((t) => t.id === tagId);
                  return tagObj ? (
                    <span key={tagId} className={styles.tag}>
                      {tagObj.name}
                      <button type="button" onClick={() => handleRemoveTag(tagId)} className={styles.tagCloseButton}>
                        ×
                      </button>
                    </span>
                  ) : null;
                })}
              </div>

              <input type="text" placeholder="Add or remove tags..." className={styles.tagInput} readOnly />

              <div className={styles.availableTags}>
                {tagOptions
                  .filter((t) => !editedTags.includes(t.id))
                  .map((option) => (
                    <span key={option.id} className={styles.availableTag} onClick={() => handleAddTag(option.id)}>
                      + {option.name}
                    </span>
                  ))}
              </div>

              <div className={styles.editorActions}>
                <Button size="small" onClick={handleCancelEditTags}>
                  Cancel
                </Button>
                <Button type="primary" size="small" onClick={handleSaveTags}>
                  Save
                </Button>
              </div>
            </div>
          )}
        </Card>
      )}

      {/* Sección Details */}
      <Card className={`${styles.detailsSection} spacing-p-16`}>
        <div className={styles.sectionHeader}>
          <ClockCircleOutlined style={{ marginRight: 8 }} />
          <Text strong>Details</Text>
        </div>
        <div className={styles.detailsGrid}>
          <div className={styles.detailItem}>
            <CalendarOutlined style={{ marginRight: 8 }} />
            <Text strong>Created:</Text>
            <Text style={{ marginLeft: 8 }}>{new Date(brief.createdAt).toLocaleDateString()}</Text>
          </div>
          {brief.expiresAt && (
            <div className={styles.detailItem}>
              <ClockCircleOutlined style={{ marginRight: 8 }} />
              <Text strong>Expires:</Text>
              <Text style={{ marginLeft: 8 }}>{new Date(brief.expiresAt).toLocaleDateString()}</Text>
            </div>
          )}
          <div className={styles.detailItem}>
            <MessageOutlined style={{ marginRight: 8 }} />
            <Text strong>Comments:</Text>
            <Text style={{ marginLeft: 8 }}>{brief.commentsCount}</Text>
          </div>
        </div>
      </Card>

      {/* Sección Comments */}
      <Card className={`${styles.commentsSection} spacing-p-16`}>
        <div className={styles.sectionHeader}>
          <MessageOutlined style={{ marginRight: 8 }} />
          <Text strong>Comments ({brief.commentsCount})</Text>
        </div>

        {/* Comentario de ejemplo */}
        {brief.commentsCount > 0 && (
          <div className={styles.comment}>
            <Text strong>I'll update our FAQ section and create talking points for this week's calls.</Text>
            <div className={styles.commentMeta}>
              <Text type="secondary">Alex Thompson (CP) • over 1 year ago</Text>
            </div>
          </div>
        )}

        {/* Input para nuevo comentario */}
        <div className={styles.addComment}>
          <TextArea
            placeholder="Add a comment..."
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            rows={3}
            style={{ marginBottom: 12 }}
          />
          <Button type="primary" onClick={handleAddComment} disabled={!newComment.trim()}>
            Add
          </Button>
        </div>
      </Card>
    </Modal>
  );
};

ViewEditBriefModal.propTypes = {
  brief: PropTypes.shape({
    id: PropTypes.number,
    title: PropTypes.string,
    description: PropTypes.string,
    type: PropTypes.string,
    priority: PropTypes.string,
    tags: PropTypes.arrayOf(PropTypes.string),
    author: PropTypes.string,
    createdAt: PropTypes.instanceOf(Date),
    expiresAt: PropTypes.instanceOf(Date),
    commentsCount: PropTypes.number,
    isPinned: PropTypes.bool,
  }),
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default ViewEditBriefModal;

import React, { useState } from 'react';
import { Modal, Input, Select, Button, Tooltip, Upload, Switch } from 'antd';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import {
  LinkOutlined,
  UploadOutlined,
  QuestionCircleOutlined,
  DownOutlined,
  CalendarOutlined,
  TeamOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  FileOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import PropTypes from 'prop-types';
import styles from './modal-brief.module.scss';

const { Option } = Select;

// Utility function to format file size
const formatFileSize = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / k ** i).toFixed(dm))} ${sizes[i]}`;
};

/**
 * CreateBriefModal - Modal component for creating new internal briefs
 *
 * @param {Object} props - Component properties
 * @param {boolean} props.open - Whether the modal is visible
 * @param {function} props.onClose - Handler to close the modal
 * @param {function} props.onSubmit - Handler for form submission
 */
const ModalBrief = ({ open, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    briefType: 'event',
    teamAssignment: 'cp-spot',
    tags: [],
    links: [],
    attachments: [],
    priorityLevel: 'normal',
    pinToTop: false,
    setExpiration: false,
  });

  const [linkInput, setLinkInput] = useState('');

  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleAddLink = () => {
    const trimmed = linkInput.trim();
    if (!trimmed) return;
    if (formData.links.includes(trimmed)) return;
    handleInputChange('links', [...formData.links, trimmed]);
    setLinkInput('');
  };

  const handleRemoveLink = (link) => {
    handleInputChange(
      'links',
      formData.links.filter((l) => l !== link)
    );
  };

  const handleRemoveAttachment = (fileToRemove) => {
    handleInputChange(
      'attachments',
      formData.attachments.filter((file) => file.uid !== fileToRemove.uid)
    );
  };

  // Opciones de tags (puedes ajustar según tus necesidades)
  const tagOptions = [
    { id: 'urgent', name: '#urgent' },
    { id: 'deadline', name: '#deadline' },
    { id: 'open-house', name: '#open-house' },
    { id: 'admissions', name: '#admissions' },
    { id: 'financial-aid', name: '#financial-aid' },
    { id: 'event', name: '#event' },
    { id: 'reminder', name: '#reminder' },
    { id: 'announcement', name: '#announcement' },
    // ...agrega más si es necesario
  ];

  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit(formData);
    }
    handleCancel();
  };

  const handleCancel = () => {
    setFormData({
      title: '',
      content: '',
      briefType: 'event',
      teamAssignment: 'cp-spot',
      tags: [],
      links: [],
      attachments: [],
      priorityLevel: 'normal',
      pinToTop: false,
      setExpiration: false,
    });
    setShowAdvanced(false);
    onClose();
  };

  const briefTypeOptions = [{ value: 'event', label: 'Event', icon: <CalendarOutlined /> }];

  const teamOptions = [{ value: 'cp-spot', label: 'CP & SPOT', icon: <TeamOutlined /> }];

  const priorityOptions = [
    { value: 'critical', label: 'Critical', icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} /> },
    { value: 'high', label: 'High', icon: <WarningOutlined style={{ color: '#faad14' }} /> },
    { value: 'normal', label: 'Normal', icon: <QuestionCircleOutlined style={{ color: '#1890ff' }} /> },
    { value: 'low', label: 'Low', icon: <QuestionCircleOutlined style={{ color: '#52c41a' }} /> },
  ];

  const uploadProps = {
    name: 'file',
    multiple: true,
    onChange(info) {
      const { fileList } = info;
      handleInputChange('attachments', fileList);
    },
    beforeUpload: () => false, // Prevent automatic upload
  };

  const quillModules = {
    toolbar: [
      [{ header: [1, 2, false] }],
      ['bold', 'italic', 'underline'],
      [{ list: 'ordered' }, { list: 'bullet' }],
      ['link'],
      ['clean'],
    ],
  };

  const quillFormats = ['header', 'bold', 'italic', 'underline', 'list', 'bullet', 'link'];

  return (
    <Modal
      title={
        <div>
          <div className={styles.modalTitle}>Create New Internal Brief</div>
          <div className={styles.subtitle}>Share important information with your team.</div>
        </div>
      }
      open={open}
      onCancel={handleCancel}
      width={700}
      className={styles.briefModal}
      footer={[
        <Button key="cancel" onClick={handleCancel} size="large">
          Cancel
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit} size="large">
          Create Brief
        </Button>,
      ]}
      centered>
      <div className={`${styles.modalContent} spacing-mt-16`}>
        {/* Brief Title */}
        <div className={styles.formGroup}>
          <label className={styles.label}>
            Brief Title <span className={styles.required}>*</span>
          </label>
          <Input
            placeholder="Enter a clear, descriptive title..."
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            size="large"
            className={styles.input}
          />
        </div>

        {/* Content */}
        <div className={styles.formGroup}>
          <label className={styles.label}>
            Content <span className={styles.required}>*</span>
          </label>
          <ReactQuill
            theme="snow"
            value={formData.content}
            onChange={(value) => handleInputChange('content', value)}
            modules={quillModules}
            formats={quillFormats}
            className={styles.textArea}
            placeholder="Write your brief content here..."
          />
        </div>

        {/* Brief Type */}
        <div className={styles.formGroup}>
          <label className={styles.label}>
            Brief Type
            <Tooltip title="Select the type of brief you're creating">
              <QuestionCircleOutlined className={styles.helpIcon} />
            </Tooltip>
          </label>
          <Select
            value={formData.briefType}
            onChange={(value) => handleInputChange('briefType', value)}
            size="large"
            className={styles.select}
            suffixIcon={<DownOutlined />}>
            {briefTypeOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                <span className={styles.optionContent}>
                  <span className={styles.optionIcon}>{option.icon}</span>
                  {option.label}
                </span>
              </Option>
            ))}
          </Select>
        </div>

        {/* Team Assignment */}
        <div className={styles.formGroup}>
          <label className={styles.label}>
            Team Assignment
            <Tooltip title="Select which team this brief is for">
              <QuestionCircleOutlined className={styles.helpIcon} />
            </Tooltip>
          </label>
          <Select
            value={formData.teamAssignment}
            onChange={(value) => handleInputChange('teamAssignment', value)}
            size="large"
            className={styles.select}
            suffixIcon={<DownOutlined />}>
            {teamOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                <span className={styles.optionContent}>
                  <span className={styles.optionIcon}>{option.icon}</span>
                  {option.label}
                </span>
              </Option>
            ))}
          </Select>
        </div>

        {/* Tags */}
        <div className={styles.formGroup}>
          <label className={styles.label}>Tags</label>
          <div className={styles.selectedTags}>
            {formData.tags.map((tagId) => {
              const tagObj = tagOptions.find((t) => t.id === tagId);
              return tagObj ? (
                <span key={tagId} className={styles.tag}>
                  {tagObj.name}
                  <button
                    type="button"
                    onClick={() => {
                      const newTags = formData.tags.filter((id) => id !== tagId);
                      handleInputChange('tags', newTags);
                    }}
                    className={styles.tagCloseButton}>
                    ×
                  </button>
                </span>
              ) : null;
            })}
          </div>
          <select
            value=""
            onChange={(e) => {
              const value = e.target.value;
              if (value && !formData.tags.includes(value)) {
                const newTags = [...formData.tags, value];
                handleInputChange('tags', newTags);
              }
              e.target.value = '';
            }}
            className={styles.selectNative}>
            <option value="">Add relevant tags...</option>
            {tagOptions
              .filter((t) => !formData.tags.includes(t.id))
              .map((option) => (
                <option key={option.id} value={option.id}>
                  {option.name}
                </option>
              ))}
          </select>
        </div>

        {/* Attachments */}
        <div className={styles.formGroup}>
          <label className={styles.label}>Attachments</label>
          <Upload {...uploadProps} className={styles.upload} showUploadList={false}>
            <Button icon={<UploadOutlined />} size="large" className={styles.uploadButton}>
              Upload Files
            </Button>
          </Upload>

          {/* Display uploaded files */}
          {formData.attachments && formData.attachments.length > 0 && (
            <div className={styles.attachmentsList}>
              {formData.attachments.map((file) => (
                <div key={file.uid} className={styles.attachmentItem}>
                  <div className={styles.attachmentInfo}>
                    <FileOutlined className={styles.attachmentIcon} />
                    <span className={styles.attachmentName}>{file.name}</span>
                    <span className={styles.attachmentSize}>{formatFileSize(file.size)}</span>
                  </div>
                  <Button
                    type="text"
                    size="small"
                    icon={<CloseOutlined />}
                    onClick={() => handleRemoveAttachment(file)}
                    className={styles.removeAttachmentButton}
                    aria-label="Remove attachment"
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Links */}
        <div className={styles.formGroup}>
          <label className={styles.label}>Links</label>
          <div className={styles.linksInputContainer}>
            <Input
              placeholder="https://example.com"
              value={linkInput}
              onChange={(e) => setLinkInput(e.target.value)}
              size="large"
              className={styles.input}
              onPressEnter={handleAddLink}
            />
            <Button
              icon={<LinkOutlined />}
              size="large"
              onClick={handleAddLink}
              className={styles.linkAddButton}
              type="default"
              aria-label="Add link"
            />
          </div>
          <div className={styles.linksList}>
            {formData.links.map((link, idx) => (
              <div key={link + idx} className={styles.linkItem}>
                <LinkOutlined className={styles.linkItemIcon} />
                <a
                  href={link.startsWith('http') ? link : `https://${link}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={styles.linkItemAnchor}>
                  {link.replace(/^https?:\/\//, '')}
                </a>
                <Button
                  type="text"
                  size="small"
                  onClick={() => handleRemoveLink(link)}
                  className={styles.removeLinkButton}
                  aria-label="Remove link">
                  ×
                </Button>
              </div>
            ))}
          </div>
        </div>

        {/* Advanced Options */}
        <div className={styles.advancedSection}>
          <Button
            type="text"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className={styles.advancedToggle}
            icon={<DownOutlined className={showAdvanced ? styles.rotated : ''} />}>
            Advanced Options
          </Button>

          {showAdvanced && (
            <div className={styles.advancedContent}>
              {/* Priority Level */}
              <div className={styles.formGroup}>
                <label className={styles.label}>
                  Priority Level
                  <Tooltip title="Set the priority level for this brief">
                    <QuestionCircleOutlined className={styles.helpIcon} />
                  </Tooltip>
                </label>
                <Select
                  value={formData.priorityLevel}
                  onChange={(value) => handleInputChange('priorityLevel', value)}
                  size="large"
                  className={styles.select}
                  suffixIcon={<DownOutlined />}>
                  {priorityOptions.map((option) => (
                    <Option key={option.value} value={option.value}>
                      <span className={styles.optionContent}>
                        <span className={styles.optionIcon}>{option.icon}</span>
                        {option.label}
                      </span>
                    </Option>
                  ))}
                </Select>
              </div>

              {/* Pin to Top */}
              <div className={styles.formGroup}>
                <div className={styles.switchContainer}>
                  <div className={styles.switchLabel}>
                    <span className={styles.label}>
                      Pin to Top
                      <Tooltip title="Pin this brief to the top of the list">
                        <QuestionCircleOutlined className={styles.helpIcon} />
                      </Tooltip>
                    </span>
                  </div>
                  <Switch
                    checked={formData.pinToTop}
                    onChange={(checked) => handleInputChange('pinToTop', checked)}
                    size="default"
                  />
                </div>
              </div>

              {/* Set Expiration */}
              <div className={styles.formGroup}>
                <div className={styles.switchContainer}>
                  <div className={styles.switchLabel}>
                    <span className={styles.label}>
                      Set Expiration
                      <Tooltip title="Set an expiration date for this brief">
                        <QuestionCircleOutlined className={styles.helpIcon} />
                      </Tooltip>
                    </span>
                  </div>
                  <Switch
                    checked={formData.setExpiration}
                    onChange={(checked) => handleInputChange('setExpiration', checked)}
                    size="default"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};

ModalBrief.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func,
};

ModalBrief.defaultProps = {
  onSubmit: null,
};

export default ModalBrief;

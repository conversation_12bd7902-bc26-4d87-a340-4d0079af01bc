import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Collapse } from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  CalendarOutlined,
  SearchOutlined,
  PushpinOutlined,
  HistoryOutlined,
} from '@ant-design/icons';
import { Card } from 'components/card/card';
import ModalBrief from './components/modal-brief';
import ViewEditBriefModal from './components/view-edit-brief-modal';
import { BriefCard } from './components';
import styles from './internal-briefs.module.scss';

export const InternalBriefs = () => {
  const [isCreateBriefModalOpen, setIsCreateBriefModalOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [selectedBrief, setSelectedBrief] = useState(null);
  const [isViewBriefModalOpen, setIsViewBriefModalOpen] = useState(false);

  const handleOpenCreateBriefModal = () => {
    setIsCreateBriefModalOpen(true);
  };

  const handleCloseCreateBriefModal = () => {
    setIsCreateBriefModalOpen(false);
  };

  const handleCreateBrief = (briefData) => {
    console.log('Creating brief:', briefData);
    // Here you would typically send the data to your API
    // For now, we'll just log it and close the modal
  };

  const handleSearchChange = (e) => {
    setSearchValue(e.target.value);
  };

  const handleSearchFocus = () => {
    setIsSearchFocused(true);
  };

  const handleSearchBlur = () => {
    // Delay to allow clicking on dropdown items
    setTimeout(() => setIsSearchFocused(false), 200);
  };

  const handleQuickSearchClick = (searchTerm) => {
    setSearchValue(searchTerm);
    setIsSearchFocused(false);
  };

  // Quick search suggestions
  const quickSearches = [
    { icon: <WarningOutlined />, text: 'Urgent', description: 'Find urgent and expiring content' },
    { icon: <ClockCircleOutlined />, text: 'Recent messages', description: 'Find messaging content from this week' },
    { icon: <CalendarOutlined />, text: 'Events', description: 'Find all event-related content' },
    { icon: <WarningOutlined />, text: 'Expiring soon', description: 'Find content expiring within 7 days' },
  ];

  // Recent searches (mock data)
  const recentSearches = ['urgent'];

  // ========== Temporal Sample brief data
  const sampleBriefs = [
    {
      id: 1,
      title: 'Spring Open House Event - March 15th',
      description:
        'Upcoming Spring Open House scheduled for March 15th, 2024. Target audience: grades 6-8. Key messaging should focus on our STEM programs and new science lab...',
      type: 'Event',
      priority: 'high',
      tags: ['open-house', 'STEM', 'grades-6-8'],
      author: 'Sarah',
      createdAt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // over 1 year ago
      commentsCount: 1,
      isPinned: true,
    },
    {
      id: 2,
      title: 'Parent Trend Alert: Concerns About Online Learning',
      description:
        'SPOT team has identified recurring concerns from parents about the balance between in-person and online learning components. 8 out of 12 recent calls mentioned this...',
      type: 'Parent Trend',
      priority: 'critical',
      tags: ['online-learning', 'parent-concerns', 'trending'],
      author: 'Jennifer',
      createdAt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // over 1 year ago
      commentsCount: 1,
      isPinned: true,
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // expires over 1 year from now
    },
    {
      id: 3,
      title: 'Updated Messaging Guidelines for Financial Aid',
      description:
        "New messaging approach for financial aid discussions based on feedback from school leadership. Focus on investment in your child's future rather than...",
      type: 'Messaging',
      priority: 'medium',
      tags: ['financial-aid', 'messaging', 'leadership-approved'],
      author: 'David',
      createdAt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // over 1 year ago
      commentsCount: 12,
      isPinned: false,
    },
    {
      id: 4,
      title: 'Updated Call Scripts and Email Templates',
      description:
        'New set of approved call scripts and email templates for Q2 campaigns. Includes personalized openers for different grade levels, objection handling for common...',
      type: 'Resource',
      priority: 'medium',
      tags: ['call-scripts', 'email-templates', 'Q2'],
      author: 'Maria',
      createdAt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // over 1 year ago
      commentsCount: 0,
      isPinned: false,
    },
    {
      id: 5,
      title: 'Enrollment Deadline Reminder Campaign',
      description:
        "Reminder: enrollment deadline is April 30th. School wants soft reminder approach - not pushy. Suggest messaging around 'securing your spot' and 'limited...",
      type: 'Messaging',
      priority: 'critical',
      tags: ['enrollment-deadline', 'april-campaign', 'testimonials'],
      author: 'Lisa',
      createdAt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // over 1 year ago
      commentsCount: 0,
      isPinned: false,
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // expires about 1 year from now
    },
    {
      id: 6,
      title: 'Miscellaneous Updates and Reminders',
      description:
        'Various small updates: parking validation forms are now available at front desk, lunch menu changes effective next week, and reminder that all staff badges must...',
      type: 'Other',
      priority: 'low',
      tags: ['updates', 'reminders', 'operations'],
      author: 'Operations',
      createdAt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // over 1 year ago
      commentsCount: 0,
      isPinned: false,
    },
    {
      id: 7,
      title: 'Summer Camp Event - Registration Opens',
      description:
        'Summer camp registration opens next week. Three program tracks available: STEM Discovery, Arts & Creativity, and Sport Leadership. Early bird pricing...',
      type: 'Event',
      priority: 'high',
      tags: ['summer-camp', 'registration', 'STEM'],
      author: 'Camp',
      createdAt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // over 1 year ago
      commentsCount: 0,
      isPinned: false,
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // expires over 1 year from now
    },
    {
      id: 8,
      title: 'URGENT: System Maintenance Tonight',
      description:
        'Emergency system maintenance scheduled for tonight 11PM-2AM. All online services will be unavailable. Please inform families calling about application submissions to...',
      type: 'Other',
      priority: 'critical',
      tags: ['urgent', 'system-maintenance', 'downtime'],
      author: 'IT',
      createdAt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // over 1 year ago
      commentsCount: 0,
      isPinned: false,
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // expires over 1 year from now
    },
    {
      id: 9,
      title: 'Fall Open House Planning Event',
      description:
        "Planning meeting for Fall Open House scheduled for next month. Need to coordinate with facilities, admissions, and teaching staff. Theme this year: 'Innovation in...",
      type: 'Event',
      priority: 'medium',
      tags: ['fall-open-house', 'planning', 'innovation'],
      author: 'Event',
      createdAt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // over 1 year ago
      commentsCount: 0,
      isPinned: false,
    },
  ];

  // Separate pinned and regular briefs
  const pinnedBriefs = sampleBriefs.filter((brief) => brief.isPinned);
  const regularBriefs = sampleBriefs.filter((brief) => !brief.isPinned);

  const handleBriefClick = (briefId) => {
    const brief = sampleBriefs.find((b) => b.id === briefId);
    if (brief) {
      setSelectedBrief(brief);
      setIsViewBriefModalOpen(true);
    }
  };

  const handleCloseViewBriefModal = () => {
    setIsViewBriefModalOpen(false);
    setSelectedBrief(null);
  };

  return (
    <>
      <div className={styles.cardsRow} style={{ width: '100%' }}>
        <Card className={`${styles.cardItem} spacing-py-8 spacing-px-16 box-shadow-hover`}>
          <Flex align="center" gap={16} className={styles.cardContent}>
            <div className={styles.iconBlueContainer}>
              <CheckCircleOutlined className={`${styles.cardIcon} ${styles.iconBlue}`} />
            </div>
            <div>
              <div className={styles.cardNumber}>9</div>
              <div className={styles.cardTitle}>Total Briefs</div>
              <div className={styles.cardSubtitle}>All active briefs</div>
            </div>
          </Flex>
        </Card>
        <Card className={`${styles.cardItem} spacing-py-8 spacing-px-16 box-shadow-hover`}>
          <Flex align="center" gap={16} className={styles.cardContent}>
            <div className={styles.iconGreenContainer}>
              <ClockCircleOutlined className={`${styles.cardIcon} ${styles.iconGreen}`} />
            </div>
            <div>
              <div className={styles.cardNumber}>0</div>
              <div className={styles.cardTitle}>New Today</div>
              <div className={styles.cardSubtitle}>Created in last 24hrs</div>
            </div>
          </Flex>
        </Card>
        <Card className={`${styles.cardItem} spacing-py-8 spacing-px-16 box-shadow-hover`}>
          <Flex align="center" gap={16} className={styles.cardContent}>
            <div className={styles.iconYellowContainer}>
              <WarningOutlined className={`${styles.cardIcon} ${styles.iconYellow}`} />
            </div>
            <div>
              <div className={styles.cardNumber}>4</div>
              <div className={styles.cardTitle}>Urgent</div>
              <div className={styles.cardSubtitle}>Expiring within 7 days</div>
            </div>
          </Flex>
        </Card>
        <Card className={`${styles.cardItem} spacing-py-8 spacing-px-16 box-shadow-hover`}>
          <Flex align="center" gap={16} className={styles.cardContent}>
            <div className={styles.iconRedContainer}>
              <CalendarOutlined className={`${styles.cardIcon} ${styles.iconRed}`} />
            </div>
            <div>
              <div className={styles.cardNumber}>4</div>
              <div className={styles.cardTitle}>Expiring Soon</div>
              <div className={styles.cardSubtitle}>Expiring within 3 days</div>
            </div>
          </Flex>
        </Card>
      </div>
      <div className={styles.briefsSection}>
        <div className={styles.briefsSearchBar}>
          <Flex gap={16} align="center" className={styles.briefsSearchBar}>
            <div className={styles.searchInputContainer}>
              <SearchOutlined className={styles.searchIcon} />
              <input
                className={`${styles.searchInput} hasIconSearch`}
                type="text"
                placeholder="Search briefs, tags, content, or try natural language..."
                value={searchValue}
                onChange={handleSearchChange}
                onFocus={handleSearchFocus}
                onBlur={handleSearchBlur}
              />
              {isSearchFocused && (
                <div className={styles.searchDropdown}>
                  <div className={styles.quickSearchSection}>
                    <div className={styles.sectionTitle}>Quick Searches</div>
                    {quickSearches.map((item, index) => (
                      <div
                        key={index}
                        className={styles.quickSearchItem}
                        onClick={() => handleQuickSearchClick(item.text)}>
                        <div className={styles.quickSearchIcon}>{item.icon}</div>
                        <div className={styles.quickSearchContent}>
                          <div className={styles.quickSearchText}>{item.text}</div>
                          <div className={styles.quickSearchDescription}>{item.description}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                  {recentSearches.length > 0 && (
                    <div className={styles.recentSearchSection}>
                      <div className={styles.sectionTitle}>Recent Searches</div>
                      {recentSearches.map((search, index) => (
                        <div
                          key={index}
                          className={styles.recentSearchItem}
                          onClick={() => handleQuickSearchClick(search)}>
                          <ClockCircleOutlined className={styles.recentSearchIcon} />
                          <span className={styles.recentSearchText}>{search}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
            <Flex gap={12} className={styles.searchActions}>
              <Button color="default" variant="outlined">
                <CheckCircleOutlined style={{ fontSize: 18, color: '#2b6ef7' }} />
                All Types
              </Button>
              <Button color="default" variant="outlined">
                <CalendarOutlined style={{ fontSize: 18, color: '#2b6ef7' }} />
                Last 60 days
              </Button>
              <Button type="primary" onClick={handleOpenCreateBriefModal}>
                + New Brief
              </Button>
            </Flex>
          </Flex>
        </div>
        {/* Brief Cards with Collapse */}
        <div className={styles.briefsContainer}>
          {sampleBriefs.length > 0 ? (
            <Collapse
              defaultActiveKey={['pinned', 'earlier']}
              ghost
              className={styles.briefsCollapse}
              items={[
                {
                  key: 'pinned',
                  label: (
                    <div className={styles.collapseHeader}>
                      <PushpinOutlined className={styles.collapseIcon} />
                      <span className={styles.collapseTitle}>Pinned Briefs</span>
                      <span className={styles.collapseCount}>{pinnedBriefs.length}</span>
                    </div>
                  ),
                  children: (
                    <div className={styles.briefsGrid}>
                      {pinnedBriefs.map((brief) => (
                        <BriefCard
                          key={brief.id}
                          id={brief.id}
                          title={brief.title}
                          description={brief.description}
                          type={brief.type}
                          priority={brief.priority}
                          tags={brief.tags}
                          author={brief.author}
                          createdAt={brief.createdAt}
                          commentsCount={brief.commentsCount}
                          isPinned={brief.isPinned}
                          onClick={handleBriefClick}
                        />
                      ))}
                    </div>
                  ),
                },
                {
                  key: 'earlier',
                  label: (
                    <div className={styles.collapseHeader}>
                      <HistoryOutlined className={styles.collapseIcon} />
                      <span className={styles.collapseTitle}>Earlier</span>
                      <span className={styles.collapseCount}>{regularBriefs.length}</span>
                    </div>
                  ),
                  children: (
                    <div className={styles.briefsGrid}>
                      {regularBriefs.map((brief) => (
                        <BriefCard
                          key={brief.id}
                          id={brief.id}
                          title={brief.title}
                          description={brief.description}
                          type={brief.type}
                          priority={brief.priority}
                          tags={brief.tags}
                          author={brief.author}
                          createdAt={brief.createdAt}
                          commentsCount={brief.commentsCount}
                          isPinned={brief.isPinned}
                          onClick={handleBriefClick}
                        />
                      ))}
                    </div>
                  ),
                },
              ]}
            />
          ) : (
            <Flex justify="center" vertical gap={16} className={styles.briefsEmptyState}>
              <div className={styles.emptyText}>No briefs match your filters</div>
              <div>
                <Button type="primary" className={styles.createBriefBtn} onClick={handleOpenCreateBriefModal}>
                  + Create First Brief
                </Button>
              </div>
            </Flex>
          )}
        </div>
      </div>

      <ModalBrief open={isCreateBriefModalOpen} onClose={handleCloseCreateBriefModal} onSubmit={handleCreateBrief} />
      <ViewEditBriefModal brief={selectedBrief} open={isViewBriefModalOpen} onClose={handleCloseViewBriefModal} />
    </>
  );
};
